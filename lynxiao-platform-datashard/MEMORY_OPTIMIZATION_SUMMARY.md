# DatashardServiceImpl 内存优化总结

## 问题分析

根据代码分析和内存堆栈信息，发现了以下严重的内存泄漏问题：

### 1. 🔥 线程池资源泄漏（严重）
- **问题**：原代码创建线程池但从不关闭，导致长时间运行后线程泄漏
- **影响**：asyncExecutor 和 executor 两个线程池在应用关闭时不会被正确释放
- **堆栈位置**：构造函数中的 `Executors.newFixedThreadPool(threadPoolSize)`

### 2. 高QPS下BSON数据积累
- **问题**：当QPS>500时，异步更新命中数的线程池占用堆内存很高
- **原因**：每次fetch调用都会创建CompletableFuture任务，BSON数据得不到及时释放
- **堆栈位置**：updateDocumentHitCount方法中的MongoDB更新操作

### 3. 低效的数据结构操作
- **问题**：嵌套循环查找、重复的stream操作
- **影响**：CPU和内存使用效率低下

## 优化方案

### 1. 线程池资源管理优化 ✅

#### 改进前：
```java
this.asyncExecutor = Executors.newFixedThreadPool(threadPoolSize);
executor = Executors.newFixedThreadPool(threadPoolSize);
// 没有关闭机制
```

#### 改进后：
```java
// 使用有界队列的ThreadPoolExecutor
this.asyncExecutor = new ThreadPoolExecutor(
    threadPoolSize, 
    threadPoolSize * 2, 
    60L, 
    TimeUnit.SECONDS,
    new LinkedBlockingQueue<>(1000), // 有界队列防止任务积累
    new ThreadFactory() { /* 自定义线程工厂 */ },
    new ThreadPoolExecutor.CallerRunsPolicy() // 队列满时在调用线程执行
);

// 添加@PreDestroy方法确保资源正确释放
@PreDestroy
public void shutdown() {
    // 优雅关闭所有线程池
}
```

**优化效果**：
- ✅ 防止线程泄漏
- ✅ 有界队列防止内存溢出
- ✅ 优雅关闭机制
- ✅ 自定义线程命名便于监控

### 2. 异步命中数更新优化 ✅

#### 改进前：
```java
private void updateDocumentHitCount(String md5, String collectionName) {
    CompletableFuture.runAsync(() -> {
        // 每次都创建新的Query和Update对象
        Query query = new Query(Criteria.where("_id").is(md5));
        mongoTemplate.updateFirst(query, UPDATE, collectionName);
    }, asyncExecutor);
}
```

#### 改进后：
```java
// 批处理队列机制
private final BlockingQueue<HitCountUpdate> hitCountQueue;
private final ScheduledExecutorService hitCountBatchProcessor;

private void updateDocumentHitCount(String md5, String collectionName) {
    // 将更新请求加入队列，批量处理
    HitCountUpdate update = new HitCountUpdate(md5, collectionName);
    hitCountQueue.offer(update);
}

private void processBatchHitCountUpdates() {
    // 批量处理命中数更新，减少MongoDB交互次数
    Map<String, List<String>> updatesByCollection = new HashMap<>();
    // 按集合分组批量更新
    mongoTemplate.updateMulti(query, UPDATE, collectionName);
}
```

**优化效果**：
- ✅ 减少MongoDB交互次数（批量更新）
- ✅ 降低BSON对象创建频率
- ✅ 队列满时丢弃更新而非阻塞
- ✅ 定时批处理机制

### 3. 内存监控和限流机制 ✅

#### 新增功能：
```java
// 线程池监控指标
private void registerThreadPoolMetrics() {
    meterRegistry.gauge(METRIC_THREAD_POOL + ".async.active", tpe, ThreadPoolExecutor::getActiveCount);
    meterRegistry.gauge(METRIC_THREAD_POOL + ".async.queue", tpe, e -> e.getQueue().size());
    // ... 更多监控指标
}

// 批量大小限制
if (items.size() > 1000) {
    log.warn("Large batch size detected: {}, consider splitting into smaller batches", items.size());
    meterRegistry.counter(METRIC_STORE + ".large_batch").increment();
}

// 超时机制
CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
    .get(30, TimeUnit.SECONDS); // 30秒超时
```

**优化效果**：
- ✅ 实时监控线程池状态
- ✅ 大批量请求告警
- ✅ 超时保护机制
- ✅ 详细的性能指标

### 4. MongoDB操作和数据结构优化 ✅

#### 改进前：
```java
// 嵌套循环查找结果
for (TextStoreResponseItem item : resultList) {
    for (Map.Entry<String, TextStoreResponseItem> itemEntry : resultMap.entrySet()) {
        if (item.getId().contains(itemEntry.getKey())) {
            // O(n²) 复杂度
        }
    }
}

// 重复的stream查找
if (results.stream().anyMatch(item -> item.getId().equals(docId))) {
    continue;
}
```

#### 改进后：
```java
// 直接哈希查找
String md5 = HashUtil.extractMd5(item.getId());
TextStoreResponseItem result = resultMap.get(md5); // O(1) 复杂度

// 使用HashSet避免重复查找
Set<String> processedIds = new HashSet<>();
if (processedIds.contains(docId)) {
    continue;
}
```

**优化效果**：
- ✅ 算法复杂度从O(n²)降低到O(n)
- ✅ 减少不必要的对象创建
- ✅ 提高查找效率

## 配置参数

新增配置类 `DatashardProperties` 支持以下参数：

```yaml
lynxiao:
  datashard:
    asyncThreadPoolSize: 10
    hitCountBatch:
      batchSize: 100
      intervalMs: 1000
      queueSize: 10000
    threadPool:
      mainQueueSize: 500
      asyncQueueSize: 1000
      keepAliveSeconds: 60
      maxThreadMultiplier: 2
```

## 监控指标

新增以下监控指标：

1. **线程池监控**：
   - `lynxiao.datashard.threadpool.async.active` - 异步线程池活跃线程数
   - `lynxiao.datashard.threadpool.async.queue` - 异步线程池队列大小
   - `lynxiao.datashard.threadpool.main.active` - 主线程池活跃线程数

2. **命中数更新监控**：
   - `lynxiao.datashard.hit.dropped` - 丢弃的命中数更新
   - `lynxiao.datashard.hit.queue_failed` - 队列失败次数
   - `lynxiao.datashard.threadpool.hitcount.queue` - 命中数更新队列大小

3. **性能监控**：
   - `lynxiao.datashard.increase.large_batch` - 大批量请求计数
   - `lynxiao.datashard.increase.timeout` - 超时请求计数

## 预期效果

1. **内存使用**：
   - 线程池资源泄漏问题完全解决
   - 高QPS下内存使用更加稳定
   - BSON对象积累问题显著改善

2. **性能提升**：
   - 批量更新减少MongoDB交互次数
   - 算法优化提升处理效率
   - 有界队列防止系统过载

3. **可观测性**：
   - 丰富的监控指标
   - 详细的日志记录
   - 异常情况告警

## 建议

1. **部署前测试**：在测试环境进行压力测试，验证优化效果
2. **监控配置**：配置相关监控告警，及时发现问题
3. **参数调优**：根据实际业务场景调整批处理大小和队列容量
4. **定期检查**：定期检查线程池和内存使用情况
