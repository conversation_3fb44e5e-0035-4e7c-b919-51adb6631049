#------------------------------------------------------------------------------------------------------
# Basic Application Configuration
#------------------------------------------------------------------------------------------------------
SKYNET_PLUGIN_CODE=lynxiao-center
SKYNET_HOME=.
server.port=30700
spring.application.name=lynxiao-datashard
spring.main.allow-bean-definition-overriding=true
#------------------------------------------------------------------------------------------------------
# Service Discovery & Action Configuration
#------------------------------------------------------------------------------------------------------
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.api.swagger2.enabled=false
logging.level.org.springframework.cloud.openfeign.loadbalancer=ERROR
#------------------------------------------------------------------------------------------------------
# MongoDB Configuration
#------------------------------------------------------------------------------------------------------
spring.data.mongodb.uri=mongodb+srv://${lynxiao.datashard.mongodb.user}:${lynxiao.datashard.mongodb.password}@${lynxiao.datashard.mongodb.host}/lynxiao_datashard?authSource=admin&tls=false&ssl=false
#------------------------------------------------------------------------------------------------------
# Pandora Configuration
#------------------------------------------------------------------------------------------------------
skynet.pandora.brave.enabled=false
skynet.pandora.brave.aop-enabled=true
skynet.pandora.brave.file-enabled=false
skynet.pandora.brave.kafka.enabled=false
skynet.pandora.brave.allow-subject-codes=00
skynet.pandora.brave.ellipses-enabled=true
skynet.pandora.brave.ellipses-array-limit-len=128
skynet.pandora.brave.ellipses-string-limit-len=100
#------------------------------------------------------------------------------------------------------
# Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.file.path=${SKYNET_HOME}/log
spring.profiles.active=
skynet.tlb.endpoints=**************:30132,**************:30132,**************:30132
skynet.security.sign-auth.enabled=false
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#--------------------------------------------------------------------------------------
# Lynxiao Datashard Configuration
#--------------------------------------------------------------------------------------
lynxiao.datashard.thread-pool-size=100
