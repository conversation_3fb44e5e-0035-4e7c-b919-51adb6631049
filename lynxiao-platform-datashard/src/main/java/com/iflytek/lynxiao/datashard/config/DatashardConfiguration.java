package com.iflytek.lynxiao.datashard.config;

import com.iflytek.lynxiao.datashard.service.DatashardService;
import com.iflytek.lynxiao.datashard.service.impl.DatashardServiceImpl;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import skynet.boot.annotation.EnableSkynetMongo;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.annotation.EnableSkynetTlbDiscoveryClient;
import skynet.boot.mongo.MongoUtils;
import skynet.boot.pandora.annotation.EnableSkynetPandora;

import javax.annotation.PostConstruct;

@Slf4j
@EnableSkynetSwagger2
@EnableSkynetMongo
@EnableSkynetPandora
@EnableSkynetTlbDiscoveryClient
@Configuration(proxyBeanMethods = false)
public class DatashardConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.datashard")
    public DatashardProperties cacheProperties() {
        return new DatashardProperties();
    }

    @Bean
    public DatashardService DatashardService(MongoTemplate mongoTemplate, MeterRegistry meterRegistry, DatashardProperties datashardProperties) {
        return new DatashardServiceImpl(mongoTemplate, meterRegistry, datashardProperties);
    }

    /**
     * 在应用启动完成后初始化Collections
     */
    @Bean
    public CollectionInitializer collectionInitializer(MongoTemplate mongoTemplate) {
        return new CollectionInitializer(mongoTemplate);
    }

    /**
     * 使用单独的类来初始化集合，避免循环依赖
     */
    public static class CollectionInitializer {

        private final MongoTemplate mongoTemplate;

        public CollectionInitializer(MongoTemplate mongoTemplate) {
            this.mongoTemplate = mongoTemplate;
        }

        @PostConstruct
        public void initCollections() {
            // Create 128 collections if they don't exist
            for (int i = 1; i <= 128; i++) {
                String collectionName = String.format("ldoc_%03d", i);
                MongoUtils.createCollectionIfNotExists(mongoTemplate, collectionName, 4096);
            }
        }
    }
}